/**
 * OCR标注工具 - UI管理器
 * 负责用户界面的更新和交互逻辑
 */

class UIManager {
    constructor() {
        this.elements = {};
        this.currentMode = 'annotation';
        this.selectedAnnotation = null;
        this.tempChildAnnotations = [];

        // 自动保存相关
        this.autoSaveEnabled = true;
        this.autoSaveInterval = 30000; // 30秒
        this.autoSaveTimer = null;
        this.unsavedChanges = new Set(); // 跟踪未保存的图片
        this.lastSaveTime = new Map(); // 记录每张图片的最后保存时间
        this.isUpdatingForm = false; // 标志是否正在更新表单

        this.initializeElements();
        this.setupEventListeners();
        this.startAutoSave();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        // 工具按钮
        this.elements.toolButtons = {
            selectMainQuestion: document.getElementById('selectMainQuestion'),
            selectSubQuestion: document.getElementById('selectSubQuestion'),
            selectAnswerArea: document.getElementById('selectAnswerArea'),
            selectImageArea: document.getElementById('selectImageArea'),
            clearSelection: document.getElementById('clearSelection')
        };

        // 表单元素（已移除左侧面板的大题信息表单）
        this.elements.forms = {};

        // 操作按钮
        this.elements.actions = {
            saveData: document.getElementById('saveData'),
            loadData: document.getElementById('loadData'),
            clearAll: document.getElementById('clearAll'),
            exportData: document.getElementById('exportData')
        };

        // 信息面板
        this.elements.panels = {
            selectedAnnotationInfo: document.getElementById('selectedAnnotationInfo'),
            hierarchySection: document.getElementById('hierarchySection'),
            hierarchyTitle: document.getElementById('hierarchyTitle'),
            hierarchyContent: document.getElementById('hierarchyContent'),
            qualityCheckInfo: document.getElementById('qualityCheckInfo'),
            currentParentHint: document.getElementById('currentParentHint'),
            currentParentName: document.getElementById('currentParentName')
        };

        // 模式选择器
        this.elements.modeSelector = document.getElementById('modeSelector');

        // 标注显示开关
        this.elements.toggleAnnotations = document.getElementById('toggleAnnotations');

        // 快捷键按钮
        this.elements.showShortcuts = document.getElementById('showShortcuts');

        // 题目计数器
        this.elements.questionCounter = document.getElementById('questionCounter');
        this.elements.currentQuestionCount = document.getElementById('currentQuestionCount');



        // 工作区相关按钮 - 简化为三个核心功能
        this.elements.openWorkspace = document.getElementById('openWorkspace');
        this.elements.saveCurrentToWorkspace = document.getElementById('saveCurrentToWorkspace');
        this.elements.clearAll = document.getElementById('clearAll');

        // 工作区状态元素
        this.elements.workspaceStatus = document.getElementById('workspaceStatus');
        this.elements.workspaceName = document.getElementById('workspaceName');
        this.elements.imageFolderStatus = document.getElementById('imageFolderStatus');
        this.elements.jsonFolderStatus = document.getElementById('jsonFolderStatus');
        this.elements.imageCount = document.getElementById('imageCount');
        this.elements.jsonCount = document.getElementById('jsonCount');
        this.elements.questionNavigation = document.getElementById('questionNavigation');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 工具按钮事件
        Object.entries(this.elements.toolButtons).forEach(([key, button]) => {
            if (button) {
                button.addEventListener('click', (e) => {
                    this.onToolButtonClick(e.target.dataset.tool || key);
                });
            }
        });

        // 表单事件（已移除左侧面板的大题信息表单事件）

        // 模式选择器事件
        if (this.elements.modeSelector) {
            this.elements.modeSelector.addEventListener('change', (e) => {
                this.onModeChange(e.target.value);
            });
        }

        // 标注显示开关事件
        if (this.elements.toggleAnnotations) {
            this.elements.toggleAnnotations.addEventListener('click', () => {
                this.onToggleAnnotations();
            });
        }

        // 快捷键按钮事件
        if (this.elements.showShortcuts) {
            this.elements.showShortcuts.addEventListener('click', () => {
                Utils.showShortcutsHelp();
            });
        }



        // 工作区按钮事件 - 简化为三个核心功能
        if (this.elements.openWorkspace) {
            this.elements.openWorkspace.addEventListener('click', () => {
                this.openWorkspace();
            });
        }

        if (this.elements.saveCurrentToWorkspace) {
            this.elements.saveCurrentToWorkspace.addEventListener('click', () => {
                this.saveCurrentToWorkspace();
            });
        }

        if (this.elements.clearAll) {
            this.elements.clearAll.addEventListener('click', () => {
                this.clearCurrentAnnotations();
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.onKeyDown(e);
        });

        // Electron菜单事件监听 - 简化
        if (window.isElectron && window.electronAPI) {
            window.electronAPI.onMenuOpenWorkspace(() => {
                this.openWorkspace();
            });

            window.electronAPI.onMenuSaveCurrent(() => {
                this.saveCurrentToWorkspace();
            });
        }
    }

    /**
     * 工具按钮点击事件
     * @param {string} tool 工具类型
     */
    onToolButtonClick(tool) {
        // 更新按钮状态
        this.updateToolButtonStates(tool === 'clearSelection' ? '' : tool);
        
        // 触发自定义事件
        this.dispatchEvent('toolSelect', { tool: tool === 'clearSelection' ? '' : tool });
    }

    /**
     * 更新工具按钮状态
     * @param {string} activeTool 激活的工具
     */
    updateToolButtonStates(activeTool) {
        Object.entries(this.elements.toolButtons).forEach(([key, button]) => {
            if (button) {
                const tool = button.dataset.tool || key;
                button.classList.toggle('active', tool === activeTool);
            }
        });
    }

    // 移除了左侧面板大题信息表单的事件处理方法
    // 大题信息现在只在右侧编辑表单中处理

    /**
     * 模式改变事件
     * @param {string} mode 模式
     */
    onModeChange(mode) {
        this.currentMode = mode;
        this.dispatchEvent('modeChange', { mode });
    }

    /**
     * 键盘事件处理
     * @param {KeyboardEvent} e 键盘事件
     */
    onKeyDown(e) {
        // 防止在输入框中触发快捷键
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        const shortcuts = {
            '1': 'main-question',
            '2': 'sub-question',
            '3': 'answer-area',
            '4': 'image-area',
            '5': 'clearSelection',
            'ArrowLeft': 'prevImage',
            'ArrowRight': 'nextImage',
            '=': 'zoomIn',
            '+': 'zoomIn',
            '-': 'zoomOut',
            '0': 'resetZoom',
            'q': 'toggleQualityCheck',
            'v': 'toggleAnnotations',
            'V': 'toggleAnnotations',
            'F1': 'showShortcuts',
            'Escape': 'clearSelection',
            'Delete': 'deleteSelected'
        };

        let action = shortcuts[e.key];
        
        // Ctrl组合键
        if (e.ctrlKey) {
            switch (e.key) {
                case 's':
                    e.preventDefault();
                    action = 'save';
                    break;
                case 'o':
                    e.preventDefault();
                    action = 'load';
                    break;
                case 'e':
                    e.preventDefault();
                    action = 'export';
                    break;
            }
        }

        if (action) {
            e.preventDefault();
            this.dispatchEvent('shortcut', { action, key: e.key, ctrlKey: e.ctrlKey });
        }
    }

    /**
     * 切换标注显示状态
     */
    onToggleAnnotations() {
        this.dispatchEvent('toggleAnnotations');
    }

    /**
     * 更新标注显示开关按钮状态
     * @param {boolean} visible 是否显示标注
     */
    updateToggleAnnotationsButton(visible) {
        const button = this.elements.toggleAnnotations;
        if (!button) return;

        const text = button.querySelector('span');

        if (visible) {
            button.classList.add('active');
            button.classList.remove('inactive');
            if (text) text.textContent = '显示标注';
            button.title = '隐藏标注 (V)';
        } else {
            button.classList.remove('active');
            button.classList.add('inactive');
            if (text) text.textContent = '隐藏标注';
            button.title = '显示标注 (V)';
        }
    }

    /**
     * 获取指定类型可以包含的子类型
     * @param {string} parentType 父级类型
     * @returns {Array} 子类型数组
     */
    getChildTypes(parentType) {
        const childTypes = [];
        const annotationTypes = {
            'main-question': { parent: null },
            'sub-question': { parent: 'main-question' },
            'answer-area': { parent: 'sub-question' },
            'image-area': { parent: 'main-question' }
        };

        for (const [type, config] of Object.entries(annotationTypes)) {
            if (config.parent === parentType) {
                childTypes.push(type);
            }
        }

        return childTypes;
    }

    /**
     * 更新父级提示显示
     * @param {Object|null} annotation 当前选中的标注
     */
    updateParentHint(annotation) {
        const hintPanel = this.elements.panels.currentParentHint;
        const nameElement = this.elements.panels.currentParentName;

        if (!hintPanel || !nameElement) return;

        if (!annotation) {
            hintPanel.style.display = 'none';
            return;
        }

        // 检查是否可以作为父级
        const canBeParent = this.getChildTypes(annotation.type).length > 0;

        if (canBeParent) {
            const typeNames = {
                'main-question': '大题',
                'sub-question': '小题',
                'answer-area': '答题区域',
                'image-area': '配图区域'
            };

            const typeName = typeNames[annotation.type] || annotation.type;
            nameElement.textContent = `${typeName}${annotation.number}`;
            hintPanel.style.display = 'block';
        } else {
            hintPanel.style.display = 'none';
        }
    }

    /**
     * 更新选中标注信息显示
     * @param {Object|null} annotation 标注对象
     */
    updateSelectedAnnotationInfo(annotation) {
        this.selectedAnnotation = annotation;
        const infoPanel = this.elements.panels.selectedAnnotationInfo;

        if (!infoPanel) return;

        // 设置更新标志，避免在表单重建时触发自动保存
        this.isUpdatingForm = true;

        // 更新父级提示
        this.updateParentHint(annotation);

        if (!annotation) {
            infoPanel.innerHTML = '<p class="no-selection">请选择一个标注区域</p>';
            this.updateNavigationSelection(null);
            this.isUpdatingForm = false;
            return;
        }

        // 同步更新导航选中状态
        this.updateNavigationSelection(annotation.id);

        const typeNames = {
            'main-question': '大题',
            'sub-question': '小题',
            'answer-area': '答题区域',
            'image-area': '配图区域'
        };

        const typeName = typeNames[annotation.type] || annotation.type;
        const coordinates = Utils.formatCoordinates(annotation.coordinates);

        let formHTML = `
            <div class="annotation-item ${annotation.type} selected">
                <div class="annotation-type">${typeName}${annotation.number}</div>
                <div class="annotation-coords">${coordinates}</div>
            </div>
            <div class="edit-form">
        `;

        // 根据标注类型生成不同的编辑表单
        switch (annotation.type) {
            case 'main-question':
                formHTML += `
                    <div class="form-group">
                        <label>题型：</label>
                        <select id="editQuestionType">
                            <option value="填空题" ${annotation.attributes.questionType === '填空题' ? 'selected' : ''}>填空题</option>
                            <option value="选择题" ${annotation.attributes.questionType === '选择题' ? 'selected' : ''}>选择题</option>
                            <option value="解答题" ${annotation.attributes.questionType === '解答题' ? 'selected' : ''}>解答题</option>
                            <option value="判断题" ${annotation.attributes.questionType === '判断题' ? 'selected' : ''}>判断题</option>
                            <option value="其他" ${annotation.attributes.questionType === '其他' ? 'selected' : ''}>其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>题干文字：</label>
                        <textarea id="editContent" placeholder="输入题干内容...">${annotation.attributes.content || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="editHasImage" ${annotation.attributes.hasImage ? 'checked' : ''}>
                            题目带配图
                        </label>
                    </div>
                `;
                break;

            case 'sub-question':
                formHTML += `
                    <div class="form-group">
                        <label>题干内容：</label>
                        <textarea id="editContent" placeholder="输入小题内容...">${annotation.attributes.content || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label>印刷手写属性：</label>
                        <select id="editPrintWriteAttribute">
                            <option value="印刷" ${annotation.attributes.printWriteAttribute === '印刷' ? 'selected' : ''}>印刷</option>
                            <option value="手写" ${annotation.attributes.printWriteAttribute === '手写' ? 'selected' : ''}>手写</option>
                        </select>
                    </div>
                `;
                break;

            case 'answer-area':
                formHTML += `
                    <div class="form-group">
                        <label>答题区域内容：</label>
                        <textarea id="editAnswerContent" placeholder="输入学生答案...">${annotation.attributes.answerContent || ''}</textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>印刷手写属性：</label>
                            <select id="editPrintWriteAttribute">
                                <option value="印刷" ${annotation.attributes.printWriteAttribute === '印刷' ? 'selected' : ''}>印刷</option>
                                <option value="手写" ${annotation.attributes.printWriteAttribute === '手写' ? 'selected' : ''}>手写</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>批改结果：</label>
                            <select id="editGradeResult">
                                <option value="正确" ${annotation.attributes.gradeResult === '正确' ? 'selected' : ''}>正确</option>
                                <option value="错误" ${annotation.attributes.gradeResult === '错误' ? 'selected' : ''}>错误</option>
                                <option value="部分正确" ${annotation.attributes.gradeResult === '部分正确' ? 'selected' : ''}>部分正确</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>正确答案：</label>
                        <textarea id="editCorrectAnswer" placeholder="输入正确答案...">${annotation.attributes.correctAnswer || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label>答案解析：</label>
                        <textarea id="editAnswerExplanation" placeholder="输入答案解析...">${annotation.attributes.answerExplanation || ''}</textarea>
                    </div>
                `;
                break;

            case 'image-area':
                formHTML += `
                    <div class="form-group">
                        <label>配图描述：</label>
                        <textarea id="editImageDescription" placeholder="输入配图描述...">${annotation.attributes.imageDescription || ''}</textarea>
                    </div>
                `;
                break;
        }

        formHTML += `
                <div class="form-actions">
                    <button type="button" class="btn btn-danger" onclick="uiManager.deleteAnnotation()">删除</button>
                    <div class="auto-save-hint">
                        <i class="icon-info"></i>
                        <span>修改内容将自动保存</span>
                    </div>
                </div>
            </div>
        `;

        infoPanel.innerHTML = formHTML;

        // 为表单字段添加自动保存事件监听器
        this.setupAutoSaveListeners(annotation);

        // 更新层级内容显示
        this.updateHierarchyContent(annotation);

        // 重置更新标志
        this.isUpdatingForm = false;
    }

    /**
     * 为表单字段设置自动保存监听器
     * @param {Object} annotation 标注对象
     */
    setupAutoSaveListeners(annotation) {
        const form = document.querySelector('.annotation-edit-form');
        if (!form) return;

        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            // 添加输入事件监听器，实现实时保存
            const saveHandler = () => {
                this.autoSaveAnnotationEdit(annotation);
            };

            // 对于不同类型的输入使用不同的事件
            if (input.type === 'text' || input.tagName === 'TEXTAREA') {
                input.addEventListener('input', saveHandler);
                input.addEventListener('blur', saveHandler); // 失去焦点时也保存
            } else if (input.tagName === 'SELECT' || input.type === 'checkbox') {
                input.addEventListener('change', saveHandler);
            }
        });
    }

    /**
     * 自动保存标注编辑
     * @param {Object} annotation 标注对象
     */
    autoSaveAnnotationEdit(annotation) {
        if (!annotation || this.isUpdatingForm) return;

        const updates = this.collectFormData(annotation);
        if (Object.keys(updates).length === 0) return;

        // 更新标注
        this.dispatchEvent('annotationUpdate', {
            annotationId: annotation.id,
            updates: updates
        });

        // 显示保存提示
        this.showAutoSaveIndicator();
    }

    /**
     * 收集表单数据
     * @param {Object} annotation 标注对象
     * @returns {Object} 更新数据
     */
    collectFormData(annotation) {
        const updates = {};

        // 根据标注类型收集表单数据
        switch (annotation.type) {
            case 'main-question':
                const questionType = document.getElementById('editQuestionType');
                const content = document.getElementById('editContent');
                const hasImage = document.getElementById('editHasImage');

                if (questionType) updates.questionType = questionType.value;
                if (content) updates.content = content.value;
                if (hasImage) updates.hasImage = hasImage.checked;
                break;

            case 'sub-question':
                const subContent = document.getElementById('editContent');
                const subPrintWrite = document.getElementById('editPrintWriteAttribute');

                if (subContent) updates.content = subContent.value;
                if (subPrintWrite) updates.printWriteAttribute = subPrintWrite.value;
                break;

            case 'answer-area':
                const answerContent = document.getElementById('editAnswerContent');
                const answerPrintWrite = document.getElementById('editPrintWriteAttribute');
                const gradeResult = document.getElementById('editGradeResult');
                const correctAnswer = document.getElementById('editCorrectAnswer');
                const answerExplanation = document.getElementById('editAnswerExplanation');

                if (answerContent) updates.answerContent = answerContent.value;
                if (answerPrintWrite) updates.printWriteAttribute = answerPrintWrite.value;
                if (gradeResult) updates.gradeResult = gradeResult.value;
                if (correctAnswer) updates.correctAnswer = correctAnswer.value;
                if (answerExplanation) updates.answerExplanation = answerExplanation.value;
                break;

            case 'image-area':
                const imageDescription = document.getElementById('editImageDescription');
                if (imageDescription) updates.imageDescription = imageDescription.value;
                break;
        }

        return updates;
    }

    /**
     * 显示自动保存指示器
     */
    showAutoSaveIndicator() {
        const hint = document.querySelector('.auto-save-hint');
        if (hint) {
            hint.style.color = '#28a745';
            hint.innerHTML = '<i class="icon-check"></i><span>已自动保存</span>';

            // 2秒后恢复原始状态
            setTimeout(() => {
                hint.style.color = '';
                hint.innerHTML = '<i class="icon-info"></i><span>修改内容将自动保存</span>';
            }, 2000);
        }
    }

    /**
     * 保存标注编辑（手动保存，保留兼容性）
     */
    saveAnnotationEdit() {
        if (!this.selectedAnnotation) return;

        const updates = this.collectFormData(this.selectedAnnotation);
        if (Object.keys(updates).length === 0) return;

        this.dispatchEvent('annotationUpdate', {
            annotationId: this.selectedAnnotation.id,
            updates: updates
        });

        Utils.showNotification('标注更新成功', 'success');
    }

    /**
     * 删除标注
     */
    deleteAnnotation() {
        if (!this.selectedAnnotation) return;

        Utils.showConfirm(
            `确定要删除这个${this.selectedAnnotation.type === 'main-question' ? '大题' : 
                           this.selectedAnnotation.type === 'sub-question' ? '小题' :
                           this.selectedAnnotation.type === 'answer-area' ? '答题区域' : '配图区域'}吗？`,
            () => {
                this.dispatchEvent('annotationDelete', { 
                    annotationId: this.selectedAnnotation.id 
                });
                this.selectedAnnotation = null;
                this.updateSelectedAnnotationInfo(null);
            }
        );
    }

    /**
     * 更新小题列表显示
     * @param {Array} subQuestions 小题数组
     */
    updateSubQuestionsList(subQuestions) {
        const listPanel = this.elements.panels.subQuestionsList;
        if (!listPanel) return;

        if (subQuestions.length === 0) {
            listPanel.innerHTML = '<p class="empty-list">暂无小题</p>';
            return;
        }

        let html = '';
        subQuestions.forEach(subQuestion => {
            const coordinates = Utils.formatCoordinates(subQuestion.coordinates);
            html += `
                <div class="annotation-item sub-question" data-id="${subQuestion.id}">
                    <div class="annotation-type">小题${subQuestion.number}</div>
                    <div class="annotation-content">${subQuestion.attributes.content || '无内容'}</div>
                    <div class="annotation-coords">${coordinates}</div>
                </div>
            `;
        });

        listPanel.innerHTML = html;

        // 添加点击事件
        listPanel.querySelectorAll('.annotation-item').forEach(item => {
            item.addEventListener('click', () => {
                const annotationId = item.dataset.id;
                this.dispatchEvent('annotationSelect', { annotationId });
            });
        });
    }

    /**
     * 更新配图列表显示
     * @param {Array} imageAreas 配图数组
     */
    updateImageAreasList(imageAreas) {
        const listPanel = this.elements.panels.imageAreasList;
        if (!listPanel) return;

        if (imageAreas.length === 0) {
            listPanel.innerHTML = '<p class="empty-list">暂无配图</p>';
            return;
        }

        let html = '';
        imageAreas.forEach(imageArea => {
            const coordinates = Utils.formatCoordinates(imageArea.coordinates);
            html += `
                <div class="annotation-item image-area" data-id="${imageArea.id}">
                    <div class="annotation-type">配图${imageArea.number}</div>
                    <div class="annotation-content">${imageArea.attributes.imageDescription || '无描述'}</div>
                    <div class="annotation-coords">${coordinates}</div>
                </div>
            `;
        });

        listPanel.innerHTML = html;

        // 添加点击事件
        listPanel.querySelectorAll('.annotation-item').forEach(item => {
            item.addEventListener('click', () => {
                const annotationId = item.dataset.id;
                this.dispatchEvent('annotationSelect', { annotationId });
            });
        });
    }

    /**
     * 更新大题信息表单（已移除左侧面板表单，现在只在右侧编辑表单中处理）
     * @param {Object} _mainQuestion 大题对象（未使用）
     */
    updateMainQuestionForm(_mainQuestion) {
        // 左侧面板的大题信息表单已移除
        // 大题信息现在只在右侧面板的编辑表单中显示和编辑
        // 保留此方法以避免破坏现有调用，但不执行任何操作
    }

    /**
     * 设置模式
     * @param {string} mode 模式
     */
    setMode(mode) {
        this.currentMode = mode;
        if (this.elements.modeSelector) {
            this.elements.modeSelector.value = mode;
        }
    }

    /**
     * 获取当前模式
     * @returns {string} 当前模式
     */
    getCurrentMode() {
        return this.currentMode;
    }

    /**
     * 更新题目计数器
     * @param {number} count 题目数量
     */
    updateQuestionCounter(count) {
        if (this.elements.currentQuestionCount) {
            this.elements.currentQuestionCount.textContent = count;
        }

        // 更新计数器的颜色和样式
        if (this.elements.questionCounter) {
            if (count === 0) {
                this.elements.questionCounter.style.background = 'rgba(108, 117, 125, 0.2)';
                this.elements.questionCounter.style.borderColor = 'rgba(108, 117, 125, 0.3)';
            } else {
                this.elements.questionCounter.style.background = 'rgba(46, 204, 113, 0.2)';
                this.elements.questionCounter.style.borderColor = 'rgba(46, 204, 113, 0.3)';
            }
        }
    }

    /**
     * 打开工作区
     */
    async openWorkspace() {
        if (window.ocrTool && window.ocrTool.workspaceManager) {
            const success = await window.ocrTool.workspaceManager.openWorkspace();
            if (success) {
                // 启用保存按钮
                if (this.elements.saveToWorkspace) {
                    this.elements.saveToWorkspace.disabled = false;
                }
            }
        } else {
            Utils.showNotification('工作区管理器未初始化', 'error');
        }
    }

    /**
     * 清除当前图片的所有标注
     */
    clearCurrentAnnotations() {
        if (!window.ocrTool || !window.ocrTool.annotationManager) {
            Utils.showNotification('标注管理器未初始化', 'error');
            return;
        }

        const annotations = window.ocrTool.annotationManager.getAllAnnotations();
        if (annotations.length === 0) {
            Utils.showNotification('当前图片没有标注', 'info');
            return;
        }

        Utils.showConfirm(
            `确定要清除当前图片的所有 ${annotations.length} 个标注吗？\n此操作不可撤销。`,
            () => {
                // 清除所有标注
                window.ocrTool.annotationManager.clear();

                // 更新UI
                this.updateSelectedAnnotationInfo(null);
                window.ocrTool.updateAnnotationLists();

                // 保存到内存（清空状态）
                window.ocrTool.saveCurrentAnnotations();

                Utils.showNotification('已清除当前图片的所有标注', 'success');
            }
        );
    }

    /**
     * 保存当前图片到工作区
     */
    async saveCurrentToWorkspace() {
        if (!window.ocrTool || !window.ocrTool.workspaceManager) {
            Utils.showNotification('工作区管理器未初始化', 'error');
            return;
        }

        const workspaceManager = window.ocrTool.workspaceManager;
        if (!workspaceManager.isWorkspaceLoaded) {
            Utils.showNotification('请先打开工作区', 'warning');
            return;
        }

        try {
            // 获取当前图片和标注数据
            const currentImage = window.ocrTool.imageManager.getCurrentImage();
            if (!currentImage) {
                Utils.showNotification('没有当前图片', 'warning');
                return;
            }

            const annotations = window.ocrTool.annotationManager.exportAnnotations();
            const mainQuestions = annotations.filter(ann => ann.type === 'main-question');

            if (mainQuestions.length === 0) {
                Utils.showNotification('没有大题标注，无法保存', 'warning');
                return;
            }

            // 为每个大题生成单独的JSON文件
            let savedCount = 0;
            for (const mainQuestion of mainQuestions) {
                const questionData = window.ocrTool.dataManager.generateJSONFromAnnotations(
                    currentImage,
                    annotations.filter(ann =>
                        ann.type === 'main-question' && ann.id === mainQuestion.id ||
                        ann.parentId === mainQuestion.id ||
                        window.ocrTool.dataManager.isAnnotationInside(ann, mainQuestion)
                    ),
                    {}
                );

                const success = await workspaceManager.saveJsonToWorkspace(
                    currentImage.name,
                    mainQuestion.number,
                    questionData
                );

                if (success) {
                    savedCount++;
                }
            }

            if (savedCount > 0) {
                Utils.showNotification(`成功保存 ${savedCount} 个JSON文件`, 'success');
            } else {
                Utils.showNotification('保存失败', 'error');
            }

        } catch (error) {
            console.error('保存到工作区失败:', error);
            Utils.showNotification('保存到工作区失败: ' + error.message, 'error');
        }
    }



    /**
     * 更新工作区状态
     * @param {Object} status 工作区状态
     */
    updateWorkspaceStatus(status) {
        if (!this.elements.workspaceStatus) return;

        if (status.isLoaded) {
            this.elements.workspaceStatus.style.display = 'block';

            if (this.elements.workspaceName) {
                this.elements.workspaceName.textContent = status.workspaceName || '未知';
            }

            if (this.elements.imageFolderStatus) {
                this.elements.imageFolderStatus.textContent = status.hasImageFolder ? '✅ 2、原始图片' : '❌ 未找到';
                this.elements.imageFolderStatus.style.color = status.hasImageFolder ? '#28a745' : '#dc3545';
            }

            if (this.elements.jsonFolderStatus) {
                this.elements.jsonFolderStatus.textContent = status.hasJsonFolder ? '✅ 1、交付json' : '❌ 未找到';
                this.elements.jsonFolderStatus.style.color = status.hasJsonFolder ? '#28a745' : '#dc3545';
            }

            if (this.elements.imageCount) {
                this.elements.imageCount.textContent = status.imageCount || 0;
            }

            if (this.elements.jsonCount) {
                this.elements.jsonCount.textContent = status.jsonFileCount || 0;
            }

            // 启用保存按钮（需要JSON文件夹存在）
            if (this.elements.saveToWorkspace) {
                this.elements.saveToWorkspace.disabled = !status.hasJsonFolder;
            }
        } else {
            this.elements.workspaceStatus.style.display = 'none';

            // 禁用保存按钮
            if (this.elements.saveToWorkspace) {
                this.elements.saveToWorkspace.disabled = true;
            }
        }
    }



    /**
     * 更新工作区按钮状态
     * @param {boolean} hasWorkspace 是否有工作区
     * @param {boolean} hasAnnotations 是否有标注
     */
    updateWorkspaceButtons(hasWorkspace, hasAnnotations) {
        // 保存按钮：需要有工作区且当前图片有标注
        if (this.elements.saveCurrentToWorkspace) {
            this.elements.saveCurrentToWorkspace.disabled = !hasWorkspace || !hasAnnotations;
        }

        // 清除按钮：只需要当前图片有标注
        if (this.elements.clearAll) {
            this.elements.clearAll.disabled = !hasAnnotations;
        }
    }

    /**
     * 显示/隐藏加载状态
     * @param {boolean} show 是否显示
     * @param {string} message 加载消息
     */
    showLoading(show, message = '加载中...') {
        // 使用通知系统显示加载状态
        if (show) {
            Utils.showNotification(message, 'info', 2000);
        }
    }

    /**
     * 派发自定义事件
     * @param {string} eventName 事件名称
     * @param {Object} detail 事件详情
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    /**
     * 更新层级内容显示
     * @param {Object|null} annotation 当前选中的标注
     */
    updateHierarchyContent(annotation) {
        const hierarchySection = this.elements.panels.hierarchySection;
        const hierarchyTitle = this.elements.panels.hierarchyTitle;
        const hierarchyContent = this.elements.panels.hierarchyContent;

        if (!hierarchySection || !hierarchyTitle || !hierarchyContent) return;

        if (!annotation) {
            hierarchySection.style.display = 'none';
            return;
        }

        // 根据标注类型显示不同的层级内容
        switch (annotation.type) {
            case 'main-question':
                this.showMainQuestionHierarchy(annotation);
                break;
            case 'sub-question':
                this.showSubQuestionHierarchy(annotation);
                break;
            case 'answer-area':
            case 'image-area':
                hierarchySection.style.display = 'none';
                break;
            default:
                hierarchySection.style.display = 'none';
        }
    }

    /**
     * 显示大题的层级内容（小题和配图）
     * @param {Object} mainQuestion 大题标注
     */
    showMainQuestionHierarchy(mainQuestion) {
        const hierarchySection = this.elements.panels.hierarchySection;
        const hierarchyTitle = this.elements.panels.hierarchyTitle;
        const hierarchyContent = this.elements.panels.hierarchyContent;

        hierarchySection.style.display = 'block';
        hierarchyTitle.textContent = `大题${mainQuestion.number} - 子内容`;

        // 获取该大题下的所有小题和配图
        const subQuestions = this.getChildAnnotations(mainQuestion.id, 'sub-question');
        const imageAreas = this.getChildAnnotations(mainQuestion.id, 'image-area');

        let html = '';

        // 显示小题列表
        if (subQuestions.length > 0) {
            html += '<div class="hierarchy-group">';
            html += '<h4 class="hierarchy-group-title">小题列表</h4>';
            subQuestions.forEach(subQuestion => {
                const coordinates = Utils.formatCoordinates(subQuestion.coordinates);
                html += `
                    <div class="annotation-item sub-question" data-id="${subQuestion.id}" onclick="uiManager.selectAnnotationById('${subQuestion.id}')">
                        <div class="annotation-type">小题${subQuestion.number}</div>
                        <div class="annotation-content">${subQuestion.attributes.content || '无内容'}</div>
                        <div class="annotation-coords">${coordinates}</div>
                    </div>
                `;
            });
            html += '</div>';
        }

        // 显示配图列表
        if (imageAreas.length > 0) {
            html += '<div class="hierarchy-group">';
            html += '<h4 class="hierarchy-group-title">配图列表</h4>';
            imageAreas.forEach(imageArea => {
                const coordinates = Utils.formatCoordinates(imageArea.coordinates);
                html += `
                    <div class="annotation-item image-area" data-id="${imageArea.id}" onclick="uiManager.selectAnnotationById('${imageArea.id}')">
                        <div class="annotation-type">配图${imageArea.number}</div>
                        <div class="annotation-content">${imageArea.attributes.imageDescription || '无描述'}</div>
                        <div class="annotation-coords">${coordinates}</div>
                    </div>
                `;
            });
            html += '</div>';
        }

        if (html === '') {
            html = '<p class="empty-list">该大题暂无小题和配图</p>';
        }

        hierarchyContent.innerHTML = html;
    }

    /**
     * 显示小题的层级内容（答题区域）
     * @param {Object} subQuestion 小题标注
     */
    showSubQuestionHierarchy(subQuestion) {
        const hierarchySection = this.elements.panels.hierarchySection;
        const hierarchyTitle = this.elements.panels.hierarchyTitle;
        const hierarchyContent = this.elements.panels.hierarchyContent;

        hierarchySection.style.display = 'block';
        hierarchyTitle.textContent = `小题${subQuestion.number} - 答题区域`;

        // 获取该小题下的所有答题区域
        const answerAreas = this.getChildAnnotations(subQuestion.id, 'answer-area');

        let html = '';

        if (answerAreas.length > 0) {
            html += '<div class="hierarchy-group">';
            html += '<h4 class="hierarchy-group-title">答题区域列表</h4>';
            answerAreas.forEach(answerArea => {
                const coordinates = Utils.formatCoordinates(answerArea.coordinates);
                const gradeResult = answerArea.attributes.gradeResult || '未批改';
                const gradeClass = gradeResult === '正确' ? 'correct' : gradeResult === '错误' ? 'incorrect' : 'partial';

                html += `
                    <div class="annotation-item answer-area" data-id="${answerArea.id}" onclick="uiManager.selectAnnotationById('${answerArea.id}')">
                        <div class="annotation-type">
                            答题区域${answerArea.number}
                            <span class="grade-badge ${gradeClass}">${gradeResult}</span>
                        </div>
                        <div class="annotation-content">${answerArea.attributes.answerContent || '无内容'}</div>
                        <div class="annotation-coords">${coordinates}</div>
                    </div>
                `;
            });
            html += '</div>';
        } else {
            html = '<p class="empty-list">该小题暂无答题区域</p>';
        }

        hierarchyContent.innerHTML = html;
    }

    /**
     * 获取指定父级下的子标注
     * @param {string} parentId 父级标注ID
     * @param {string} type 子标注类型
     * @returns {Array} 子标注数组
     */
    getChildAnnotations(parentId, type) {
        // 这个方法需要从主应用获取标注数据
        // 通过事件系统请求数据
        const event = new CustomEvent('getChildAnnotations', {
            detail: { parentId, type }
        });
        document.dispatchEvent(event);

        // 返回临时存储的结果
        return this.tempChildAnnotations || [];
    }

    /**
     * 根据ID选择标注
     * @param {string} annotationId 标注ID
     */
    selectAnnotationById(annotationId) {
        this.dispatchEvent('annotationSelect', { annotationId });
    }

    /**
     * 更新题目导航
     * @param {Array} annotations 标注数组
     */
    updateQuestionNavigation(annotations) {
        if (!this.elements.questionNavigation) return;

        const mainQuestions = annotations.filter(ann => ann.type === 'main-question')
            .sort((a, b) => a.number - b.number);

        if (mainQuestions.length === 0) {
            this.elements.questionNavigation.innerHTML = '<p class="empty-navigation">当前图片没有题目</p>';
            return;
        }

        let navigationHTML = '';

        mainQuestions.forEach(mainQuestion => {
            const subQuestions = annotations.filter(ann =>
                ann.type === 'sub-question' && ann.parentId === mainQuestion.id
            ).sort((a, b) => a.number - b.number);

            // 配图区域直接属于大题
            const imageAreas = annotations.filter(ann =>
                ann.type === 'image-area' && ann.parentId === mainQuestion.id
            ).sort((a, b) => a.number - b.number);

            const hasChildren = subQuestions.length > 0 || imageAreas.length > 0;
            const content = `大题${mainQuestion.number}`;

            navigationHTML += `
                <div class="nav-item" data-annotation-id="${mainQuestion.id}">
                    <div class="nav-item-header" onclick="uiManager.onNavigationItemClick('${mainQuestion.id}')">
                        ${hasChildren ? `<span class="nav-item-toggle" onclick="uiManager.toggleNavigationItem(event, '${mainQuestion.id}')">▶</span>` : '<span class="nav-item-toggle"></span>'}
                        <span class="nav-item-content" title="${content}">${content}</span>
                        <span class="nav-item-type">大题</span>
                    </div>
                    ${hasChildren ? `<div class="nav-children" id="nav-children-${mainQuestion.id}">` : ''}
            `;

            // 添加子题目及其答题区域
            subQuestions.forEach(subQuestion => {
                const subContent = `小题${subQuestion.number}`;
                navigationHTML += `
                    <div class="nav-child nav-sub-question" data-annotation-id="${subQuestion.id}" onclick="uiManager.onNavigationItemClick('${subQuestion.id}')">
                        <span class="nav-child-content" title="${subContent}">${subContent}</span>
                        <span class="nav-child-type">小题</span>
                    </div>
                `;

                // 添加该小题的答题区域
                const subAnswerAreas = annotations.filter(ann =>
                    ann.type === 'answer-area' && ann.parentId === subQuestion.id
                ).sort((a, b) => a.number - b.number);

                subAnswerAreas.forEach(answerArea => {
                    const answerContent = `答题区域${answerArea.number}`;
                    navigationHTML += `
                        <div class="nav-child nav-answer-area" data-annotation-id="${answerArea.id}" onclick="uiManager.onNavigationItemClick('${answerArea.id}')" style="padding-left: 48px;">
                            <span class="nav-child-content" title="${answerContent}">${answerContent}</span>
                            <span class="nav-child-type">答题</span>
                        </div>
                    `;
                });
            });

            // 添加配图区域（直接属于大题）
            imageAreas.forEach(imageArea => {
                const imageContent = `配图区域${imageArea.number}`;
                navigationHTML += `
                    <div class="nav-child nav-image-area" data-annotation-id="${imageArea.id}" onclick="uiManager.onNavigationItemClick('${imageArea.id}')">
                        <span class="nav-child-content" title="${imageContent}">${imageContent}</span>
                        <span class="nav-child-type">配图</span>
                    </div>
                `;
            });

            if (hasChildren) {
                navigationHTML += '</div>';
            }
            navigationHTML += '</div>';
        });

        this.elements.questionNavigation.innerHTML = navigationHTML;
    }

    /**
     * 导航项点击事件
     * @param {string} annotationId 标注ID
     */
    onNavigationItemClick(annotationId) {
        // 清除之前的选中状态
        const prevSelected = this.elements.questionNavigation.querySelectorAll('.selected');
        prevSelected.forEach(el => el.classList.remove('selected'));

        // 设置当前选中状态
        const currentItem = this.elements.questionNavigation.querySelector(`[data-annotation-id="${annotationId}"]`);
        if (currentItem) {
            const header = currentItem.classList.contains('nav-item') ?
                currentItem.querySelector('.nav-item-header') : currentItem;
            header.classList.add('selected');
        }

        // 触发标注选择事件
        this.dispatchEvent('annotationSelect', { annotationId });
    }

    /**
     * 切换导航项展开/折叠
     * @param {Event} event 事件对象
     * @param {string} annotationId 标注ID
     */
    toggleNavigationItem(event, annotationId) {
        event.stopPropagation(); // 阻止冒泡到父级点击事件

        const toggle = event.target;
        const children = document.getElementById(`nav-children-${annotationId}`);

        if (children) {
            const isExpanded = children.classList.contains('expanded');

            if (isExpanded) {
                children.classList.remove('expanded');
                toggle.classList.remove('expanded');
                toggle.textContent = '▶';
            } else {
                children.classList.add('expanded');
                toggle.classList.add('expanded');
                toggle.textContent = '▼';
            }
        }
    }

    /**
     * 更新导航选中状态
     * @param {string|null} annotationId 标注ID
     */
    updateNavigationSelection(annotationId) {
        if (!this.elements.questionNavigation) return;

        // 清除之前的选中状态
        const prevSelected = this.elements.questionNavigation.querySelectorAll('.selected');
        prevSelected.forEach(el => el.classList.remove('selected'));

        if (annotationId) {
            // 设置当前选中状态
            const currentItem = this.elements.questionNavigation.querySelector(`[data-annotation-id="${annotationId}"]`);
            if (currentItem) {
                const header = currentItem.classList.contains('nav-item') ?
                    currentItem.querySelector('.nav-item-header') : currentItem;
                if (header) {
                    header.classList.add('selected');

                    // 如果是子项，确保父项展开
                    if (currentItem.classList.contains('nav-child')) {
                        const parentItem = currentItem.closest('.nav-item');
                        if (parentItem) {
                            const parentId = parentItem.dataset.annotationId;
                            const children = document.getElementById(`nav-children-${parentId}`);
                            const toggle = parentItem.querySelector('.nav-item-toggle');

                            if (children && !children.classList.contains('expanded')) {
                                children.classList.add('expanded');
                                toggle.classList.add('expanded');
                                toggle.textContent = '▼';
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 启动自动保存
     */
    startAutoSave() {
        if (!this.autoSaveEnabled) return;

        this.autoSaveTimer = setInterval(() => {
            this.performAutoSave();
        }, this.autoSaveInterval);

        console.log('自动保存已启动，间隔:', this.autoSaveInterval / 1000, '秒');
    }

    /**
     * 停止自动保存
     */
    stopAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
            this.autoSaveTimer = null;
        }
    }

    /**
     * 执行自动保存
     */
    async performAutoSave() {
        if (!window.ocrTool || !window.ocrTool.workspaceManager) {
            return;
        }

        const workspaceManager = window.ocrTool.workspaceManager;
        if (!workspaceManager.isWorkspaceLoaded) {
            return;
        }

        // 检查是否有未保存的更改
        if (this.unsavedChanges.size === 0) {
            return;
        }

        try {
            let savedCount = 0;
            const imagesToSave = Array.from(this.unsavedChanges);

            for (const imageKey of imagesToSave) {
                const success = await this.saveImageAnnotations(imageKey);
                if (success) {
                    savedCount++;
                    this.unsavedChanges.delete(imageKey);
                    this.lastSaveTime.set(imageKey, Date.now());
                }
            }

            if (savedCount > 0) {
                this.showSaveStatus(`自动保存: ${savedCount} 张图片`, 'success');
                console.log(`自动保存完成: ${savedCount} 张图片`);
            }

        } catch (error) {
            console.error('自动保存失败:', error);
            this.showSaveStatus('自动保存失败', 'error');
        }
    }

    /**
     * 保存指定图片的标注数据
     * @param {string} imageKey 图片键
     */
    async saveImageAnnotations(imageKey) {
        if (!window.ocrTool || !window.ocrTool.annotationData) {
            return false;
        }

        const annotations = window.ocrTool.annotationData.get(imageKey);
        if (!annotations || annotations.length === 0) {
            return false;
        }

        const imageName = imageKey.split('|')[0];
        const mainQuestions = annotations.filter(ann => ann.type === 'main-question');

        if (mainQuestions.length === 0) {
            return false;
        }

        let savedCount = 0;
        for (const mainQuestion of mainQuestions) {
            const questionData = window.ocrTool.dataManager.generateJSONFromAnnotations(
                { name: imageName },
                annotations.filter(ann =>
                    ann.type === 'main-question' && ann.id === mainQuestion.id ||
                    ann.parentId === mainQuestion.id ||
                    window.ocrTool.dataManager.isAnnotationInside(ann, mainQuestion)
                ),
                {}
            );

            const success = await window.ocrTool.workspaceManager.saveJsonToWorkspace(
                imageName,
                mainQuestion.number,
                questionData
            );

            if (success) {
                savedCount++;
            }
        }

        return savedCount > 0;
    }

    /**
     * 标记图片有未保存的更改
     * @param {string} imageKey 图片键
     */
    markUnsavedChanges(imageKey) {
        if (imageKey) {
            this.unsavedChanges.add(imageKey);
            this.updateSaveStatus();
        }
    }

    /**
     * 更新保存状态显示
     */
    updateSaveStatus() {
        const unsavedCount = this.unsavedChanges.size;
        if (unsavedCount > 0) {
            this.showSaveStatus(`${unsavedCount} 张图片有未保存更改`, 'warning');
        } else {
            this.showSaveStatus('所有更改已保存', 'success');
        }
    }

    /**
     * 显示保存状态
     * @param {string} message 消息
     * @param {string} type 类型
     */
    showSaveStatus(message, type) {
        // 可以在状态栏或通知区域显示保存状态
        const statusElement = document.getElementById('saveStatus');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `save-status ${type}`;
        }
    }

    /**
     * 销毁UI管理器
     */
    destroy() {
        this.stopAutoSave();
        this.elements = {};
        this.selectedAnnotation = null;
        this.unsavedChanges.clear();
        this.lastSaveTime.clear();
    }
}

// 创建全局UI管理器实例
window.uiManager = new UIManager();
