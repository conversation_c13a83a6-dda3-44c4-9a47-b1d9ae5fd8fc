/**
 * OCR标注工具 - 标注管理器
 * 负责标注的创建、编辑、删除和绘制
 */

class AnnotationManager {
    constructor(coordinateSystem) {
        this.coordinateSystem = coordinateSystem;
        this.annotations = new Map();
        this.currentTool = '';
        this.selectedAnnotation = null;
        this.isDrawing = false;
        this.drawingStart = null;
        this.drawingCurrent = null;

        // 显示控制
        this.annotationsVisible = true;
        
        // 标注类型配置
        this.annotationTypes = {
            'main-question': {
                name: '大题',
                color: '#e74c3c',
                maxCount: -1, // 允许多个大题
                parent: null
            },
            'sub-question': {
                name: '小题',
                color: '#2ecc71',
                maxCount: -1,
                parent: 'main-question'
            },
            'answer-area': {
                name: '答题区域',
                color: '#9b59b6',
                maxCount: -1,
                parent: 'sub-question'
            },
            'image-area': {
                name: '配图区域',
                color: '#f39c12',
                maxCount: -1,
                parent: 'main-question'
            }
        };

        // 计数器
        this.counters = {
            'main-question': 0,
            'sub-question': 0,
            'answer-area': 0,
            'image-area': 0
        };

        this.callbacks = {
            onAnnotationCreate: [],
            onAnnotationUpdate: [],
            onAnnotationDelete: [],
            onAnnotationSelect: []
        };

        this.setupEventListeners();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        const canvas = this.coordinateSystem.canvasElement;
        if (!canvas) return;

        canvas.addEventListener('mousedown', (e) => this.onMouseDown(e));
        canvas.addEventListener('mousemove', (e) => this.onMouseMove(e));
        canvas.addEventListener('mouseup', (e) => this.onMouseUp(e));
        canvas.addEventListener('click', (e) => this.onClick(e));
        
        // 防止右键菜单
        canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    /**
     * 添加回调函数
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * 触发回调函数
     * @param {string} event 事件名称
     * @param {any} data 传递的数据
     */
    trigger(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => callback(data));
        }
    }

    /**
     * 设置当前工具
     * @param {string} tool 工具类型
     */
    setCurrentTool(tool) {
        console.log(`设置工具: ${tool}, 当前选中标注:`, this.selectedAnnotation?.type, this.selectedAnnotation?.id);
        this.currentTool = tool;
        // 不要清除选中状态，保持选中的标注作为潜在父级
        // this.selectedAnnotation = null;
        this.updateCursor();
        this.redraw();
    }

    /**
     * 清除选择（工具和选中标注）
     */
    clearSelection() {
        console.log('清除所有选择');
        this.currentTool = '';
        this.selectAnnotation(null);
        this.updateCursor();
        this.redraw();
    }

    /**
     * 更新鼠标样式
     */
    updateCursor() {
        const canvas = this.coordinateSystem.canvasElement;
        if (!canvas) return;

        if (this.currentTool) {
            canvas.style.cursor = 'crosshair';
            document.body.classList.add('selecting');
        } else {
            canvas.style.cursor = 'default';
            document.body.classList.remove('selecting');
        }
    }

    /**
     * 鼠标按下事件
     * @param {MouseEvent} e 鼠标事件
     */
    onMouseDown(e) {
        // 如果是右键、中键或Ctrl+左键，不处理标注操作（用于平移）
        if (e.button !== 0 || e.ctrlKey) return;

        const imageCoords = this.coordinateSystem.eventToImage(e);

        if (this.currentTool) {
            // 开始绘制新标注
            this.startDrawing(imageCoords);
        } else {
            // 选择已有标注
            this.selectAnnotationAt(imageCoords);
        }
    }

    /**
     * 鼠标移动事件
     * @param {MouseEvent} e 鼠标事件
     */
    onMouseMove(e) {
        const imageCoords = this.coordinateSystem.eventToImage(e);
        
        // 更新坐标显示
        this.updateCoordinateDisplay(imageCoords);

        if (this.isDrawing && this.currentTool) {
            this.updateDrawing(imageCoords);
        }
    }

    /**
     * 鼠标抬起事件
     * @param {MouseEvent} e 鼠标事件
     */
    onMouseUp(e) {
        if (e.button !== 0) return;

        if (this.isDrawing && this.currentTool) {
            const imageCoords = this.coordinateSystem.eventToImage(e);
            this.finishDrawing(imageCoords);
        }
    }

    /**
     * 鼠标点击事件
     * @param {MouseEvent} e 鼠标事件
     */
    onClick(e) {
        // 处理单击选择
        if (!this.currentTool && !this.isDrawing) {
            const imageCoords = this.coordinateSystem.eventToImage(e);
            this.selectAnnotationAt(imageCoords);
        }
    }

    /**
     * 开始绘制标注
     * @param {Object} coords 起始坐标
     */
    startDrawing(coords) {
        if (!this.coordinateSystem.isInImageBounds(coords.x, coords.y)) return;

        this.isDrawing = true;
        this.drawingStart = coords;
        this.drawingCurrent = coords;
    }

    /**
     * 更新绘制过程
     * @param {Object} coords 当前坐标
     */
    updateDrawing(coords) {
        if (!this.isDrawing) return;

        // 限制在图片范围内
        const clampedCoords = this.coordinateSystem.clampToImageBounds(coords.x, coords.y);
        this.drawingCurrent = clampedCoords;
        
        this.redraw();
    }

    /**
     * 完成绘制
     * @param {Object} coords 结束坐标
     */
    finishDrawing(coords) {
        if (!this.isDrawing || !this.drawingStart) return;

        const clampedCoords = this.coordinateSystem.clampToImageBounds(coords.x, coords.y);
        
        // 检查最小尺寸
        const minSize = 10; // 最小10像素
        const width = Math.abs(clampedCoords.x - this.drawingStart.x);
        const height = Math.abs(clampedCoords.y - this.drawingStart.y);
        
        if (width < minSize || height < minSize) {
            Utils.showNotification('标注区域太小，请重新绘制', 'warning');
            this.cancelDrawing();
            return;
        }

        // 创建标注
        const coordinates = [
            [Math.round(this.drawingStart.x), Math.round(this.drawingStart.y)],
            [Math.round(clampedCoords.x), Math.round(clampedCoords.y)]
        ];

        this.createAnnotation(this.currentTool, coordinates);
        this.cancelDrawing();
    }

    /**
     * 取消绘制
     */
    cancelDrawing() {
        this.isDrawing = false;
        this.drawingStart = null;
        this.drawingCurrent = null;
        this.redraw();
    }

    /**
     * 创建标注
     * @param {string} type 标注类型
     * @param {Array} coordinates 坐标数组
     * @param {Object} attributes 属性对象
     */
    createAnnotation(type, coordinates, attributes = {}) {
        const typeConfig = this.annotationTypes[type];
        if (!typeConfig) {
            console.error('未知的标注类型:', type);
            return null;
        }

        // 检查数量限制
        if (typeConfig.maxCount > 0) {
            const existingCount = this.getAnnotationsByType(type).length;
            if (existingCount >= typeConfig.maxCount) {
                Utils.showNotification(`${typeConfig.name}最多只能有${typeConfig.maxCount}个`, 'warning');
                return null;
            }
        }

        // 检查父级依赖
        if (typeConfig.parent && !this.hasAnnotationType(typeConfig.parent)) {
            Utils.showNotification(`请先创建${this.annotationTypes[typeConfig.parent].name}`, 'warning');
            return null;
        }

        // 生成ID和编号
        const id = Utils.generateId();
        this.counters[type]++;
        const number = this.counters[type];

        const annotation = {
            id: id,
            type: type,
            number: number,
            coordinates: coordinates,
            attributes: {
                content: '',
                printWriteAttribute: '印刷',
                ...attributes
            },
            parentId: this.findParentId(type, coordinates),
            children: [],
            selected: false,
            created: Date.now(),
            updated: Date.now()
        };

        // 添加类型特定属性
        this.addTypeSpecificAttributes(annotation);

        this.annotations.set(id, annotation);
        this.updateParentChildRelations(annotation);
        this.selectAnnotation(annotation);
        this.redraw();
        
        this.trigger('onAnnotationCreate', annotation);
        Utils.showNotification(`创建${typeConfig.name}成功`, 'success');
        
        return annotation;
    }

    /**
     * 添加类型特定属性
     * @param {Object} annotation 标注对象
     */
    addTypeSpecificAttributes(annotation) {
        switch (annotation.type) {
            case 'main-question':
                annotation.attributes.questionType = '填空题';
                annotation.attributes.hasImage = false;
                break;
            case 'answer-area':
                annotation.attributes.answerContent = '';
                annotation.attributes.gradeResult = '正确';
                annotation.attributes.correctAnswer = '';
                annotation.attributes.answerExplanation = '';
                break;
            case 'image-area':
                annotation.attributes.imageDescription = '';
                break;
        }
    }

    /**
     * 查找父级ID
     * @param {string} type 标注类型
     * @param {Array} coordinates 坐标
     * @returns {string|null} 父级ID
     */
    findParentId(type, coordinates) {
        const typeConfig = this.annotationTypes[type];
        if (!typeConfig.parent) return null;

        const parentAnnotations = this.getAnnotationsByType(typeConfig.parent);

        console.log(`查找${type}的父级，候选父级数量:`, parentAnnotations.length);
        console.log('当前选中的标注:', this.selectedAnnotation?.type, this.selectedAnnotation?.id);

        // 优先级1：如果当前选中的标注是合适的父级类型，则使用它
        if (this.selectedAnnotation &&
            this.selectedAnnotation.type === typeConfig.parent) {
            console.log('使用选中的标注作为父级:', this.selectedAnnotation.id);
            return this.selectedAnnotation.id;
        }

        // 优先级2：找到包含当前坐标的父级标注
        for (const parent of parentAnnotations) {
            if (this.isCoordinatesInside(coordinates, parent.coordinates)) {
                console.log('找到包含关系的父级:', parent.id);
                return parent.id;
            }
        }

        // 优先级3：找到距离最近的父级标注
        if (parentAnnotations.length > 0) {
            const nearestParent = this.findNearestParent(coordinates, parentAnnotations);
            console.log('使用距离最近的父级:', nearestParent.id);
            return nearestParent.id;
        }

        console.log('没有找到合适的父级');
        return null;
    }

    /**
     * 找到距离最近的父级标注
     * @param {Array} coordinates 子标注坐标
     * @param {Array} parentAnnotations 候选父级标注数组
     * @returns {Object} 最近的父级标注
     */
    findNearestParent(coordinates, parentAnnotations) {
        const [childStart, childEnd] = coordinates;
        const childCenterX = (childStart[0] + childEnd[0]) / 2;
        const childCenterY = (childStart[1] + childEnd[1]) / 2;

        let nearestParent = parentAnnotations[0];
        let minDistance = Infinity;

        for (const parent of parentAnnotations) {
            const [parentStart, parentEnd] = parent.coordinates;
            const parentCenterX = (parentStart[0] + parentEnd[0]) / 2;
            const parentCenterY = (parentStart[1] + parentEnd[1]) / 2;

            const distance = Math.sqrt(
                Math.pow(childCenterX - parentCenterX, 2) +
                Math.pow(childCenterY - parentCenterY, 2)
            );

            if (distance < minDistance) {
                minDistance = distance;
                nearestParent = parent;
            }
        }

        return nearestParent;
    }

    /**
     * 检查坐标是否在另一个坐标内（放宽的包含关系）
     * @param {Array} inner 内部坐标
     * @param {Array} outer 外部坐标
     * @returns {boolean} 是否有关联关系
     */
    isCoordinatesInside(inner, outer) {
        const [innerStart, innerEnd] = inner;
        const [outerStart, outerEnd] = outer;

        const innerRect = {
            x: Math.min(innerStart[0], innerEnd[0]),
            y: Math.min(innerStart[1], innerEnd[1]),
            width: Math.abs(innerEnd[0] - innerStart[0]),
            height: Math.abs(innerEnd[1] - innerStart[1])
        };

        const outerRect = {
            x: Math.min(outerStart[0], outerEnd[0]),
            y: Math.min(outerStart[1], outerEnd[1]),
            width: Math.abs(outerEnd[0] - outerStart[0]),
            height: Math.abs(outerEnd[1] - outerStart[1])
        };

        // 使用放宽的包含关系检查
        return this.hasRelationship(innerRect, outerRect);
    }

    /**
     * 检查两个矩形是否有关联关系（放宽的包含检查）
     * @param {Object} inner 内部矩形
     * @param {Object} outer 外部矩形
     * @returns {boolean} 是否有关联关系
     */
    hasRelationship(inner, outer) {
        // 策略1：完全包含（严格包含）
        const strictContainment = inner.x >= outer.x &&
                                 inner.y >= outer.y &&
                                 inner.x + inner.width <= outer.x + outer.width &&
                                 inner.y + inner.height <= outer.y + outer.height;

        if (strictContainment) {
            console.log('使用严格包含关系');
            return true;
        }

        // 策略2：重叠关系（有交集即可）
        const hasOverlap = !(inner.x + inner.width < outer.x ||
                           outer.x + outer.width < inner.x ||
                           inner.y + inner.height < outer.y ||
                           outer.y + outer.height < inner.y);

        if (hasOverlap) {
            // 计算重叠面积比例
            const overlapArea = this.calculateOverlapArea(inner, outer);
            const innerArea = inner.width * inner.height;
            const overlapRatio = overlapArea / innerArea;

            // 如果重叠面积超过内部矩形的30%，认为有关联关系
            if (overlapRatio >= 0.3) {
                console.log(`使用重叠关系，重叠比例: ${(overlapRatio * 100).toFixed(1)}%`);
                return true;
            }
        }

        // 策略3：垂直对齐关系（适用于题目编号等场景）
        const verticalAlignment = this.checkVerticalAlignment(inner, outer);
        if (verticalAlignment) {
            console.log('使用垂直对齐关系');
            return true;
        }

        return false;
    }

    /**
     * 计算两个矩形的重叠面积
     * @param {Object} rect1 矩形1
     * @param {Object} rect2 矩形2
     * @returns {number} 重叠面积
     */
    calculateOverlapArea(rect1, rect2) {
        const left = Math.max(rect1.x, rect2.x);
        const right = Math.min(rect1.x + rect1.width, rect2.x + rect2.width);
        const top = Math.max(rect1.y, rect2.y);
        const bottom = Math.min(rect1.y + rect1.height, rect2.y + rect2.height);

        if (left < right && top < bottom) {
            return (right - left) * (bottom - top);
        }
        return 0;
    }

    /**
     * 检查垂直对齐关系
     * @param {Object} inner 内部矩形
     * @param {Object} outer 外部矩形
     * @returns {boolean} 是否垂直对齐
     */
    checkVerticalAlignment(inner, outer) {
        // 检查Y轴范围是否有重叠
        const yOverlap = !(inner.y + inner.height < outer.y || outer.y + outer.height < inner.y);

        if (!yOverlap) return false;

        // 检查水平距离是否在合理范围内（比如100像素内）
        const maxDistance = 100;
        const horizontalDistance = Math.min(
            Math.abs(inner.x - (outer.x + outer.width)), // inner在outer右侧的距离
            Math.abs((inner.x + inner.width) - outer.x)   // inner在outer左侧的距离
        );

        return horizontalDistance <= maxDistance;
    }

    /**
     * 更新父子关系
     * @param {Object} annotation 标注对象
     */
    updateParentChildRelations(annotation) {
        if (annotation.parentId) {
            const parent = this.annotations.get(annotation.parentId);
            if (parent && !parent.children.includes(annotation.id)) {
                parent.children.push(annotation.id);
            }
        }
    }

    /**
     * 在指定位置选择标注
     * @param {Object} coords 坐标
     */
    selectAnnotationAt(coords) {
        const annotation = this.findAnnotationAt(coords);
        this.selectAnnotation(annotation);
    }

    /**
     * 查找指定位置的标注
     * @param {Object} coords 坐标
     * @returns {Object|null} 标注对象
     */
    findAnnotationAt(coords) {
        const candidateAnnotations = [];

        // 找到所有包含该点的标注
        for (const annotation of this.annotations.values()) {
            if (this.isPointInAnnotation(coords, annotation)) {
                candidateAnnotations.push(annotation);
            }
        }

        if (candidateAnnotations.length === 0) {
            return null;
        }

        // 如果只有一个候选，直接返回
        if (candidateAnnotations.length === 1) {
            return candidateAnnotations[0];
        }

        // 多个候选时，优先选择面积最小的（通常是子标注）
        candidateAnnotations.sort((a, b) => {
            const areaA = this.getAnnotationArea(a);
            const areaB = this.getAnnotationArea(b);

            // 面积相同时，按创建时间倒序（后创建的优先）
            if (areaA === areaB) {
                return b.created - a.created;
            }

            return areaA - areaB;
        });

        return candidateAnnotations[0];
    }

    /**
     * 计算标注的面积
     * @param {Object} annotation 标注对象
     * @returns {number} 面积
     */
    getAnnotationArea(annotation) {
        const [start, end] = annotation.coordinates;
        const width = Math.abs(end[0] - start[0]);
        const height = Math.abs(end[1] - start[1]);
        return width * height;
    }

    /**
     * 检查点是否在标注内
     * @param {Object} point 点坐标
     * @param {Object} annotation 标注对象
     * @returns {boolean} 是否在标注内
     */
    isPointInAnnotation(point, annotation) {
        const [start, end] = annotation.coordinates;
        const rect = {
            x: Math.min(start[0], end[0]),
            y: Math.min(start[1], end[1]),
            width: Math.abs(end[0] - start[0]),
            height: Math.abs(end[1] - start[1])
        };

        return Utils.isPointInRect(point, rect);
    }

    /**
     * 选择标注
     * @param {Object|null} annotation 标注对象
     */
    selectAnnotation(annotation) {
        console.log('选择标注:', annotation?.type, annotation?.id);

        // 取消之前的选择
        if (this.selectedAnnotation) {
            this.selectedAnnotation.selected = false;
        }

        this.selectedAnnotation = annotation;

        if (annotation) {
            annotation.selected = true;
        }

        this.redraw();
        this.trigger('onAnnotationSelect', annotation);
    }

    /**
     * 更新标注
     * @param {string} id 标注ID
     * @param {Object} updates 更新内容
     */
    updateAnnotation(id, updates) {
        const annotation = this.annotations.get(id);
        if (!annotation) return;

        Object.assign(annotation.attributes, updates);
        annotation.updated = Date.now();
        
        this.trigger('onAnnotationUpdate', annotation);
    }

    /**
     * 删除标注
     * @param {string} id 标注ID
     */
    deleteAnnotation(id) {
        const annotation = this.annotations.get(id);
        if (!annotation) return;

        // 删除子标注
        for (const childId of annotation.children) {
            this.deleteAnnotation(childId);
        }

        // 从父标注中移除
        if (annotation.parentId) {
            const parent = this.annotations.get(annotation.parentId);
            if (parent) {
                parent.children = parent.children.filter(childId => childId !== id);
            }
        }

        this.annotations.delete(id);
        
        if (this.selectedAnnotation && this.selectedAnnotation.id === id) {
            this.selectedAnnotation = null;
        }

        this.redraw();
        this.trigger('onAnnotationDelete', annotation);
    }

    /**
     * 获取指定类型的标注
     * @param {string} type 标注类型
     * @returns {Array} 标注数组
     */
    getAnnotationsByType(type) {
        return Array.from(this.annotations.values()).filter(ann => ann.type === type);
    }

    /**
     * 检查是否存在指定类型的标注
     * @param {string} type 标注类型
     * @returns {boolean} 是否存在
     */
    hasAnnotationType(type) {
        return this.getAnnotationsByType(type).length > 0;
    }

    /**
     * 更新坐标显示
     * @param {Object} coords 坐标
     */
    updateCoordinateDisplay(coords) {
        // 坐标显示已移除，保留方法以避免破坏现有调用
        // 如果需要显示坐标，可以在工具栏或其他位置添加
    }

    /**
     * 重绘所有标注
     */
    redraw() {
        const canvas = this.coordinateSystem.canvasElement;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 只有在标注可见时才绘制
        if (this.annotationsVisible) {
            // 绘制所有标注
            for (const annotation of this.annotations.values()) {
                this.drawAnnotation(ctx, annotation);
            }

            // 绘制正在绘制的标注
            if (this.isDrawing && this.drawingStart && this.drawingCurrent) {
                this.drawDrawingAnnotation(ctx);
            }
        }
    }

    /**
     * 切换标注显示状态
     * @param {boolean} visible 是否显示标注
     */
    setAnnotationsVisible(visible) {
        this.annotationsVisible = visible;
        this.redraw();
    }

    /**
     * 获取标注显示状态
     * @returns {boolean} 是否显示标注
     */
    getAnnotationsVisible() {
        return this.annotationsVisible;
    }

    /**
     * 切换标注显示状态
     */
    toggleAnnotationsVisibility() {
        this.annotationsVisible = !this.annotationsVisible;
        this.redraw();
        return this.annotationsVisible;
    }

    /**
     * 绘制标注
     * @param {CanvasRenderingContext2D} ctx 画布上下文
     * @param {Object} annotation 标注对象
     */
    drawAnnotation(ctx, annotation) {
        const typeConfig = this.annotationTypes[annotation.type];
        if (!typeConfig) return;

        const rect = this.coordinateSystem.coordinatesToRect(annotation.coordinates);
        if (rect.width === 0 || rect.height === 0) return;

        // 检查标注框是否在画布可见区域内
        const clippedRect = this.clipRectToCanvas(rect);
        if (clippedRect.width <= 0 || clippedRect.height <= 0) return;

        // 设置样式
        ctx.strokeStyle = typeConfig.color;
        ctx.fillStyle = typeConfig.color + '33'; // 半透明填充
        ctx.lineWidth = annotation.selected ? 3 : 2;
        ctx.setLineDash(annotation.selected ? [] : [5, 5]);

        // 绘制矩形（使用裁剪后的坐标）
        ctx.fillRect(clippedRect.x, clippedRect.y, clippedRect.width, clippedRect.height);
        ctx.strokeRect(clippedRect.x, clippedRect.y, clippedRect.width, clippedRect.height);

        // 绘制标签（只有当标注框足够大时才绘制）
        if (clippedRect.width > 20 && clippedRect.height > 20) {
            this.drawLabel(ctx, annotation, clippedRect);
        }
    }

    /**
     * 将矩形裁剪到画布可见区域内
     * @param {Object} rect 原始矩形 {x, y, width, height}
     * @returns {Object} 裁剪后的矩形 {x, y, width, height}
     */
    clipRectToCanvas(rect) {
        const canvas = this.coordinateSystem.canvasElement;
        if (!canvas) return rect;

        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;

        // 计算裁剪后的坐标
        const x1 = Math.max(0, rect.x);
        const y1 = Math.max(0, rect.y);
        const x2 = Math.min(canvasWidth, rect.x + rect.width);
        const y2 = Math.min(canvasHeight, rect.y + rect.height);

        return {
            x: x1,
            y: y1,
            width: Math.max(0, x2 - x1),
            height: Math.max(0, y2 - y1)
        };
    }

    /**
     * 绘制正在绘制的标注
     * @param {CanvasRenderingContext2D} ctx 画布上下文
     */
    drawDrawingAnnotation(ctx) {
        const typeConfig = this.annotationTypes[this.currentTool];
        if (!typeConfig) return;

        const startCanvas = this.coordinateSystem.imageToCanvas(this.drawingStart.x, this.drawingStart.y);
        const currentCanvas = this.coordinateSystem.imageToCanvas(this.drawingCurrent.x, this.drawingCurrent.y);

        const rect = {
            x: Math.min(startCanvas.x, currentCanvas.x),
            y: Math.min(startCanvas.y, currentCanvas.y),
            width: Math.abs(currentCanvas.x - startCanvas.x),
            height: Math.abs(currentCanvas.y - startCanvas.y)
        };

        // 检查绘制中的标注框是否在画布可见区域内
        const clippedRect = this.clipRectToCanvas(rect);
        if (clippedRect.width <= 0 || clippedRect.height <= 0) return;

        ctx.strokeStyle = typeConfig.color;
        ctx.fillStyle = typeConfig.color + '22';
        ctx.lineWidth = 2;
        ctx.setLineDash([3, 3]);

        ctx.fillRect(clippedRect.x, clippedRect.y, clippedRect.width, clippedRect.height);
        ctx.strokeRect(clippedRect.x, clippedRect.y, clippedRect.width, clippedRect.height);
    }

    /**
     * 绘制标签
     * @param {CanvasRenderingContext2D} ctx 画布上下文
     * @param {Object} annotation 标注对象
     * @param {Object} rect 矩形区域
     */
    drawLabel(ctx, annotation, rect) {
        const typeConfig = this.annotationTypes[annotation.type];
        const label = `${typeConfig.name}${annotation.number}`;

        ctx.font = '12px Microsoft YaHei';
        ctx.fillStyle = 'white';
        ctx.strokeStyle = typeConfig.color;
        ctx.lineWidth = 1;
        ctx.setLineDash([]);

        const textWidth = ctx.measureText(label).width;
        const labelX = rect.x;
        const labelY = rect.y - 5;

        // 绘制标签背景
        ctx.fillStyle = typeConfig.color;
        ctx.fillRect(labelX, labelY - 15, textWidth + 8, 18);

        // 绘制标签文字
        ctx.fillStyle = 'white';
        ctx.fillText(label, labelX + 4, labelY - 2);
    }

    /**
     * 获取所有标注
     * @returns {Array} 标注数组
     */
    getAllAnnotations() {
        return Array.from(this.annotations.values());
    }

    /**
     * 获取选中的标注
     * @returns {Object|null} 选中的标注
     */
    getSelectedAnnotation() {
        return this.selectedAnnotation;
    }

    /**
     * 清空所有标注
     */
    clear() {
        this.annotations.clear();
        this.selectedAnnotation = null;
        this.counters = {
            'main-question': 0,
            'sub-question': 0,
            'answer-area': 0,
            'image-area': 0
        };
        this.redraw();
    }

    /**
     * 加载标注数据
     * @param {Array} annotationsData 标注数据数组
     */
    loadAnnotations(annotationsData) {
        this.clear();

        // 第一步：加载所有标注对象
        for (const data of annotationsData) {
            const annotation = {
                ...data,
                children: [], // 重置children数组
                selected: false,
                created: data.created || Date.now(),
                updated: data.updated || Date.now()
            };

            this.annotations.set(annotation.id, annotation);
            this.counters[annotation.type] = Math.max(this.counters[annotation.type], annotation.number);
        }

        // 第二步：重新建立父子关系
        for (const annotation of this.annotations.values()) {
            if (annotation.parentId) {
                const parent = this.annotations.get(annotation.parentId);
                if (parent && !parent.children.includes(annotation.id)) {
                    parent.children.push(annotation.id);
                }
            }
        }

        console.log(`加载了 ${this.annotations.size} 个标注，重新建立了父子关系`);
        this.redraw();
    }

    /**
     * 导出标注数据
     * @returns {Array} 标注数据数组
     */
    exportAnnotations() {
        return Array.from(this.annotations.values()).map(annotation => ({
            ...annotation,
            selected: false // 不导出选中状态
        }));
    }

    /**
     * 获取标注统计信息
     * @returns {Object} 统计信息
     */
    getStatistics() {
        const stats = {
            total: this.annotations.size,
            byType: {}
        };

        for (const [type, config] of Object.entries(this.annotationTypes)) {
            stats.byType[type] = {
                name: config.name,
                count: this.getAnnotationsByType(type).length,
                color: config.color
            };
        }

        return stats;
    }

    /**
     * 验证标注完整性
     * @returns {Object} 验证结果
     */
    validateAnnotations() {
        const errors = [];
        const warnings = [];

        // 检查必需的标注类型
        if (!this.hasAnnotationType('main-question')) {
            errors.push('缺少大题标注');
        }

        if (!this.hasAnnotationType('sub-question')) {
            errors.push('至少需要一个小题标注');
        }

        // 检查每个标注的完整性
        for (const annotation of this.annotations.values()) {
            const typeConfig = this.annotationTypes[annotation.type];

            // 检查坐标有效性
            if (!Utils.isValidCoordinates(annotation.coordinates)) {
                errors.push(`${typeConfig.name}${annotation.number}的坐标无效`);
            }

            // 检查内容完整性
            if (!annotation.attributes.content && annotation.type !== 'image-area') {
                warnings.push(`${typeConfig.name}${annotation.number}的内容为空`);
            }

            // 检查答题区域特定属性
            if (annotation.type === 'answer-area') {
                if (!annotation.attributes.answerContent) {
                    warnings.push(`答题区域${annotation.number}的答案内容为空`);
                }
                if (!annotation.attributes.correctAnswer) {
                    warnings.push(`答题区域${annotation.number}的正确答案为空`);
                }
            }
        }

        return {
            valid: errors.length === 0,
            errors: errors,
            warnings: warnings
        };
    }

    /**
     * 自动生成引用
     * 在题干中自动添加{答题区域N}和{图N}引用
     */
    generateReferences() {
        const mainQuestion = this.getAnnotationsByType('main-question')[0];
        if (!mainQuestion) return;

        const subQuestions = this.getAnnotationsByType('sub-question');

        for (const subQuestion of subQuestions) {
            let content = subQuestion.attributes.content || '';

            // 获取该小题的答题区域
            const answerAreas = this.getAllAnnotations()
                .filter(ann => ann.type === 'answer-area' && ann.parentId === subQuestion.id)
                .sort((a, b) => a.number - b.number);

            // 获取配图区域
            const imageAreas = this.getAnnotationsByType('image-area')
                .sort((a, b) => a.number - b.number);

            // 添加答题区域引用
            for (const answerArea of answerAreas) {
                const reference = `{答题区域${answerArea.number}}`;
                if (!content.includes(reference)) {
                    content += ` ${reference}`;
                }
            }

            // 添加配图引用
            for (const imageArea of imageAreas) {
                const reference = `{图${imageArea.number}}`;
                if (!content.includes(reference)) {
                    content = `${reference} ${content}`;
                }
            }

            subQuestion.attributes.content = content.trim();
            subQuestion.updated = Date.now();
        }

        // 不触发更新事件，避免无限循环
        // this.trigger('onAnnotationUpdate', null);
    }

    /**
     * 获取指定父级下的子标注
     * @param {string} parentId 父级标注ID
     * @param {string} type 子标注类型（可选）
     * @returns {Array} 子标注数组
     */
    getChildAnnotations(parentId, type = null) {
        const parent = this.annotations.get(parentId);
        if (!parent || !parent.children) return [];

        const childAnnotations = [];

        for (const childId of parent.children) {
            const child = this.annotations.get(childId);
            if (child && (!type || child.type === type)) {
                childAnnotations.push(child);
            }
        }

        // 按编号排序
        return childAnnotations.sort((a, b) => a.number - b.number);
    }

    /**
     * 根据ID获取标注
     * @param {string} id 标注ID
     * @returns {Object|null} 标注对象
     */
    getAnnotationById(id) {
        return this.annotations.get(id) || null;
    }

    /**
     * 根据ID选择标注
     * @param {string} id 标注ID
     */
    selectAnnotationById(id) {
        const annotation = this.getAnnotationById(id);
        this.selectAnnotation(annotation);
    }

    /**
     * 复制标注
     * @param {string} id 标注ID
     * @returns {Object|null} 新标注对象
     */
    duplicateAnnotation(id) {
        const original = this.annotations.get(id);
        if (!original) return null;

        const newCoordinates = [
            [original.coordinates[0][0] + 20, original.coordinates[0][1] + 20],
            [original.coordinates[1][0] + 20, original.coordinates[1][1] + 20]
        ];

        return this.createAnnotation(original.type, newCoordinates, { ...original.attributes });
    }

    /**
     * 移动标注
     * @param {string} id 标注ID
     * @param {number} deltaX X轴偏移
     * @param {number} deltaY Y轴偏移
     */
    moveAnnotation(id, deltaX, deltaY) {
        const annotation = this.annotations.get(id);
        if (!annotation) return;

        const newCoordinates = [
            [annotation.coordinates[0][0] + deltaX, annotation.coordinates[0][1] + deltaY],
            [annotation.coordinates[1][0] + deltaX, annotation.coordinates[1][1] + deltaY]
        ];

        // 检查是否在图片范围内
        const [start, end] = newCoordinates;
        if (this.coordinateSystem.isInImageBounds(start[0], start[1]) &&
            this.coordinateSystem.isInImageBounds(end[0], end[1])) {
            annotation.coordinates = newCoordinates;
            annotation.updated = Date.now();
            this.redraw();
            this.trigger('onAnnotationUpdate', annotation);
        }
    }

    /**
     * 调整标注大小
     * @param {string} id 标注ID
     * @param {number} deltaWidth 宽度变化
     * @param {number} deltaHeight 高度变化
     */
    resizeAnnotation(id, deltaWidth, deltaHeight) {
        const annotation = this.annotations.get(id);
        if (!annotation) return;

        const [start, end] = annotation.coordinates;
        const newEnd = [end[0] + deltaWidth, end[1] + deltaHeight];

        // 检查最小尺寸和边界
        const minSize = 10;
        const width = Math.abs(newEnd[0] - start[0]);
        const height = Math.abs(newEnd[1] - start[1]);

        if (width >= minSize && height >= minSize &&
            this.coordinateSystem.isInImageBounds(newEnd[0], newEnd[1])) {
            annotation.coordinates = [start, newEnd];
            annotation.updated = Date.now();
            this.redraw();
            this.trigger('onAnnotationUpdate', annotation);
        }
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.clear();
        this.callbacks = {
            onAnnotationCreate: [],
            onAnnotationUpdate: [],
            onAnnotationDelete: [],
            onAnnotationSelect: []
        };
    }
}

// 导出标注管理器类
window.AnnotationManager = AnnotationManager;
