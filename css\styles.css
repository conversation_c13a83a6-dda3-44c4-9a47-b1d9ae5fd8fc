/* OCR标注工具 - 主样式文件 */

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f5f5f5;
    overflow: hidden;
}

/* 色彩变量 */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --background-color: #f5f5f5;
    --border-color: #ddd;
    --text-color: #333;
    --text-light: #666;
    --text-muted: #999;
    
    /* 标注颜色 */
    --main-question-color: #e74c3c;
    --sub-question-color: #2ecc71;
    --answer-area-color: #9b59b6;
    --image-area-color: #f39c12;
    
    /* 间距 */
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    
    /* 布局尺寸 */
    --toolbar-height: 60px;
    --left-panel-width: 240px;
    --right-panel-width: 380px;
}

/* 布局结构 */
.toolbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--toolbar-height);
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-md);
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.main-content {
    position: fixed;
    top: var(--toolbar-height);
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    overflow: hidden;
}

.left-panel {
    width: var(--left-panel-width);
    background: white;
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
    flex-shrink: 0;
}

.image-container {
    flex: 1;
    position: relative;
    background: #f8f9fa;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.right-panel {
    width: var(--right-panel-width);
    background: white;
    border-left: 1px solid var(--border-color);
    overflow-y: auto;
    flex-shrink: 0;
}



/* 工具栏样式 */
.toolbar-left,
.toolbar-center,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.toolbar-center {
    flex: 1;
    justify-content: center;
}

.image-counter {
    background: rgba(255,255,255,0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    font-size: 12px;
}

.question-counter {
    color: rgba(255,255,255,0.9);
    font-size: 12px;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(46, 204, 113, 0.2);
    border: 1px solid rgba(46, 204, 113, 0.3);
    border-radius: 4px;
    margin-left: var(--spacing-sm);
}

.question-counter:hover {
    background: rgba(46, 204, 113, 0.3);
}

.image-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 13px;
    max-width: 300px;
}

.separator {
    color: rgba(255,255,255,0.5);
}

.zoom-controls {
    display: flex;
    gap: 2px;
}

.mode-selector {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    font-size: 12px;
}

.mode-selector option {
    background: var(--primary-color);
    color: white;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    white-space: nowrap;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: rgba(101, 2, 206, 0.5);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-secondary:hover {
    background: rgba(101, 2, 206, 0.6);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #229954;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-info {
    background: var(--secondary-color);
    color: white;
}

.btn-info:hover {
    background: #2980b9;
}

.btn-nav,
.btn-zoom {
    background: rgba(255,255,255,0.1);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
    padding: var(--spacing-xs);
    width: 32px;
    height: 32px;
    justify-content: center;
}

.btn-nav:hover,
.btn-zoom:hover {
    background: rgba(255,255,255,0.2);
}

.btn-help {
    background: rgba(255,255,255,0.1);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
    font-size: 12px;
}

.btn-help:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.3);
}

.btn-toggle {
    background: rgba(255,255,255,0.1);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
    font-size: 12px;
    transition: all 0.3s ease;
}

.btn-toggle:hover {
    background: rgba(255,255,255,0.2);
}

.btn-toggle.active {
    background: var(--success-color);
    border-color: var(--success-color);
}

.btn-toggle.active:hover {
    background: #229954;
}

.btn-toggle:not(.active) {
    background: rgba(231, 76, 60, 0.8);
    border-color: rgba(231, 76, 60, 0.8);
}

.btn-toggle:not(.active):hover {
    background: rgba(231, 76, 60, 0.9);
}

/* 面板样式 */
.panel-section {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.panel-section:last-child {
    border-bottom: none;
}

.panel-section h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

/* 工具按钮 */
.tool-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
}

.tool-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: white;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
}

.tool-btn:hover {
    border-color: var(--secondary-color);
    background: #f8f9fa;
}

.tool-btn.active {
    border-color: var(--secondary-color);
    background: var(--secondary-color);
    color: white;
}

.tool-btn.clear-btn {
    grid-column: 1 / -1;
    flex-direction: row;
    justify-content: center;
}

/* 表单样式 */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: inherit;
    font-size: 13px;
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

/* 特殊内容框的高度调整 */
#editContent,
#editAnswerContent,
#editCorrectAnswer,
#editAnswerExplanation {
    min-height: 140px;
}

#editImageDescription {
    min-height: 100px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 复选框样式优化 */
.form-group label input[type="checkbox"] {
    width: auto;
    margin-right: var(--spacing-sm);
    vertical-align: middle;
}

.form-group label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    cursor: pointer;
    font-weight: 400;
}

/* 为了兼容不支持:has()的浏览器，添加备用样式 */
.form-group .checkbox-label {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    cursor: pointer;
    font-weight: 400;
}

.form-group .checkbox-label input[type="checkbox"] {
    width: auto;
    margin-right: var(--spacing-sm);
    vertical-align: middle;
    flex-shrink: 0;
}

/* 复选框悬停效果 */
.form-group .checkbox-label:hover {
    color: var(--secondary-color);
}

.form-group .checkbox-label input[type="checkbox"]:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* 操作按钮 */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
}

.action-buttons .btn {
    justify-content: center;
    padding: var(--spacing-sm);
}

/* 图片容器 */
.image-wrapper {
    position: relative;
    max-width: 100%;
    max-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

#currentImage {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    user-select: none;
    pointer-events: none;
}

#annotationCanvas {
    position: absolute;
    top: 0;
    left: 0;
    cursor: crosshair;
    pointer-events: auto;
}

/* 拖拽区域 */
.drop-zone {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.9);
    border: 2px dashed var(--border-color);
    transition: all 0.3s ease;
}

.drop-zone.drag-over {
    border-color: var(--secondary-color);
    background: rgba(52, 152, 219, 0.1);
}

.drop-zone.hidden {
    display: none;
}

.drop-zone-content {
    text-align: center;
    color: var(--text-muted);
}

.drop-zone-content i {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
}

/* 列表样式 */
.questions-list,
.images-list {
    max-height: 200px;
    overflow-y: auto;
}

.empty-list {
    color: var(--text-muted);
    font-style: italic;
    text-align: center;
    padding: var(--spacing-md);
}

/* 标注信息 */
.annotation-info {
    font-size: 13px;
}

.no-selection {
    color: var(--text-muted);
    font-style: italic;
    text-align: center;
    padding: var(--spacing-md);
}

/* 父级提示样式 */
.parent-hint {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 8px 0;
    font-size: 12px;
    color: #1976d2;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.parent-hint::before {
    content: "💡";
    font-size: 14px;
}

/* 父子关系提示面板 */
.parent-hint-panel {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #e9ecef;
}

.hint-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 12px;
    line-height: 1.4;
}

.hint-item:last-child {
    margin-bottom: 0;
}

.hint-icon {
    font-size: 14px;
    flex-shrink: 0;
    margin-top: 1px;
}

.hint-text {
    color: #495057;
}

.current-parent-hint {
    background: linear-gradient(135deg, #e8f5e8, #f0f8ff);
    border: 1px solid #28a745;
    border-radius: 4px;
    padding: 8px;
    margin-top: 8px;
    animation: pulse 2s infinite;
}

.current-parent-hint .hint-text {
    color: #155724;
    font-weight: 500;
}

#currentParentName {
    font-weight: bold;
    color: #007bff;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    :root {
        --left-panel-width: 220px;
        --right-panel-width: 340px;
    }
}

@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        height: auto;
        padding: var(--spacing-sm);
    }
    
    .toolbar-left,
    .toolbar-center,
    .toolbar-right {
        width: 100%;
        justify-content: center;
        margin-bottom: var(--spacing-xs);
    }
    
    .main-content {
        flex-direction: column;
    }
    
    .left-panel,
    .right-panel {
        width: 100%;
        height: 200px;
    }
}

/* 选择状态样式 */
body.selecting {
    cursor: crosshair !important;
}

body.selecting * {
    cursor: crosshair !important;
}

/* 平移状态样式 */
body.panning {
    cursor: grabbing !important;
    user-select: none !important;
}

body.panning * {
    cursor: grabbing !important;
}

.image-container:hover {
    cursor: grab;
}

.image-container.panning {
    cursor: grabbing !important;
}

/* 图片容器平移提示 */
.image-wrapper {
    position: relative;
}

.image-wrapper::after {
    content: "右键或Ctrl+拖拽可移动图片";
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 10;
}

.image-wrapper:hover::after {
    opacity: 1;
}

/* 保存状态样式 */
.save-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.save-status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.save-status.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    animation: pulse 2s infinite;
}

.save-status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.save-status.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 脉冲动画用于警告状态 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
